console.log("🚀 Teste rápido do sistema");
console.log("Node.js:", process.version);

try {
  const puppeteer = require("puppeteer");
  console.log("✅ Puppeteer carregado com sucesso!");
  
  const fs = require("fs");
  console.log("✅ Módulo fs carregado!");
  
  const readline = require("readline");
  console.log("✅ Módulo readline carregado!");
  
  console.log("\n🎉 Todos os módulos necessários estão funcionando!");
  console.log("\n📋 Para executar o script principal:");
  console.log("   node print.js");
  
} catch (error) {
  console.error("❌ Erro:", error.message);
  console.log("\n🔧 Execute: npm install");
}
