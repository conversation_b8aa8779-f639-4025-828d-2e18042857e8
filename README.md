# Doity - Script de Impressão Automática de Etiquetas

Este script automatiza o processo de impressão de etiquetas no sistema Doity (doity.com.br).

## 📋 Funcionalidades

- **Login automático** no sistema Doity
- **Navegação automática** pelas páginas de credenciamento
- **Impressão automática** de etiquetas para todos os inscritos
- **Controle manual** da paginação
- **Remoção automática** do chat flutuante que pode interferir
- **Detecção automática** do Chrome instalado no sistema

## 🚀 Como usar

### 1. Instalação das dependências
```bash
npm install
```

### 2. Executar o script

**Opção recomendada (com verificações automáticas):**
```bash
npm start
```

**Opções alternativas:**
```bash
# Executar diretamente (sem verificações)
npm run print
# ou
node print.js

# Apenas testar o sistema
npm test
```

### 3. Interação durante a execução

1. O script abrirá o Chrome automaticamente
2. Fará login no sistema Doity
3. Perguntará qual página começar (digite o número)
4. Para cada página:
   - Imprimirá todas as etiquetas automaticamente
   - Perguntará se quer continuar para a próxima página
   - Pressione ENTER para continuar ou digite 'sair' para encerrar

## ⚙️ Configuração

### Credenciais
As credenciais estão hardcoded no script:
- **Usuário**: <EMAIL>
- **Senha**: Davicomunic22#

### ID do Evento
O script está configurado para o evento ID: **263089**

Para alterar essas configurações, edite o arquivo `print.js`.

## 🔧 Requisitos Técnicos

- **Node.js** (versão 14 ou superior)
- **Google Chrome** instalado no sistema
- **Conexão com internet**
- **Windows** (o script detecta automaticamente os caminhos do Chrome no Windows)

## 📁 Estrutura do Projeto

```
doity-windows/
├── print.js          # Script principal
├── package.json       # Configurações do projeto
├── README.md         # Este arquivo
└── node_modules/     # Dependências (criado após npm install)
```

## 🛠️ Dependências

- **puppeteer**: Controle automatizado do navegador Chrome
- **readline**: Interface para entrada de dados do usuário
- **fs**: Sistema de arquivos (nativo do Node.js)

## ⚠️ Observações Importantes

1. **Impressão**: O script usa `--kiosk-printing` para impressão silenciosa
2. **Headless**: O navegador roda em modo visível (`headless: false`) para permitir interação
3. **Timing**: Há um delay de 2 segundos entre cada impressão para estabilidade
4. **Chrome**: O script tenta encontrar o Chrome em caminhos padrão do Windows

## 🐛 Solução de Problemas

### Timeout de navegação
**Erro**: `Navigation timeout of 30000 ms exceeded`
**Solução**:
- Verifique sua conexão com internet
- O script agora tem timeout aumentado para 60 segundos
- Tente executar novamente

### Chrome não encontrado
Se o Chrome não for encontrado, o script usará o Chromium do Puppeteer automaticamente.

### Erro de política de execução (PowerShell)
**Erro**: `execução de scripts foi desabilitada neste sistema`
**Solução**: Use: `powershell -ExecutionPolicy Bypass -Command "npm start"`

### Problemas de rede
**Sintomas**: Erro ao conectar com Doity
**Soluções**:
- Verifique sua conexão com internet
- Verifique se o site doity.com.br está acessível
- Tente usar uma VPN se houver bloqueios regionais

### Problemas de login
**Sintomas**: Script trava na tela de login
**Soluções**:
- Verifique se as credenciais estão corretas no arquivo `print.js`
- Verifique se não há captcha ou autenticação de dois fatores ativa
- Tente fazer login manual primeiro para verificar a conta

### Script não encontra botões de impressão
**Sintomas**: "Imprimindo inscrito" mas nada acontece
**Soluções**:
- Verifique se está na página correta do evento (ID: 263089)
- Verifique se há inscritos na página atual
- Tente uma página diferente
