const puppeteer = require("puppeteer");
const fs = require("fs");
const { spawn } = require("child_process");

console.log("🚀 Doity - Script de Impressão de Etiquetas");
console.log("==========================================");

async function checkSystem() {
  console.log("\n🔍 Verificando sistema...");
  
  // Verificar Node.js
  console.log(`✅ Node.js: ${process.version}`);
  
  // Verificar Puppeteer
  try {
    const puppeteerVersion = require("puppeteer/package.json").version;
    console.log(`✅ Puppeteer: v${puppeteerVersion}`);
  } catch (error) {
    console.log("❌ Puppeteer não encontrado!");
    return false;
  }
  
  // Verificar Chrome
  const chromePaths = [
    "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
    "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
    process.env.LOCALAPPDATA + "\\Google\\Chrome\\Application\\chrome.exe"
  ];

  let chromeFound = false;
  for (const path of chromePaths) {
    if (fs.existsSync(path)) {
      console.log(`✅ Chrome encontrado: ${path}`);
      chromeFound = true;
      break;
    }
  }

  if (!chromeFound) {
    console.log("⚠️  Chrome não encontrado. Usando Chromium do Puppeteer.");
  }
  
  return true;
}

async function testConnection() {
  console.log("\n🌐 Testando conexão com Doity...");
  
  try {
    const browser = await puppeteer.launch({
      headless: true,
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
      timeout: 30000
    });
    
    const page = await browser.newPage();
    page.setDefaultNavigationTimeout(30000);
    
    await page.goto("https://doity.com.br", { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    console.log("✅ Conexão com Doity estabelecida!");
    await browser.close();
    return true;
    
  } catch (error) {
    console.log("❌ Erro ao conectar com Doity:", error.message);
    console.log("⚠️  Verifique sua conexão com a internet");
    return false;
  }
}

async function main() {
  const systemOk = await checkSystem();
  if (!systemOk) {
    console.log("\n❌ Sistema não está configurado corretamente!");
    console.log("Execute: npm install");
    return;
  }
  
  const connectionOk = await testConnection();
  if (!connectionOk) {
    console.log("\n⚠️  Problemas de conexão detectados, mas continuando...");
  }
  
  console.log("\n🎉 Sistema pronto!");
  console.log("\n📋 Instruções:");
  console.log("1. O script abrirá o Chrome automaticamente");
  console.log("2. Fará login no sistema Doity");
  console.log("3. Perguntará qual página começar");
  console.log("4. Imprimirá todas as etiquetas automaticamente");
  console.log("5. Perguntará se quer continuar para próxima página");
  
  console.log("\n🚀 Iniciando script principal...");
  console.log("==========================================\n");
  
  // Executar o script principal
  const child = spawn("node", ["print.js"], {
    stdio: "inherit",
    cwd: process.cwd()
  });
  
  child.on("error", (error) => {
    console.error("❌ Erro ao executar script:", error);
  });
  
  child.on("close", (code) => {
    if (code !== 0) {
      console.log(`\n⚠️  Script finalizado com código: ${code}`);
    } else {
      console.log("\n✅ Script finalizado com sucesso!");
    }
  });
}

main().catch(console.error);
