const puppeteer = require("puppeteer");
const readline = require("readline");
const fs = require("fs");

function askQuestion(query) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  return new Promise(resolve => rl.question(query, ans => {
    rl.close();
    resolve(ans);
  }))
}

(async () => {
  console.log("Iniciando o script...");
  console.log("Abrindo o navegador...");

  // Caminhos possíveis para o Chrome
  const chromePaths = [
    "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
    "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
    process.env.LOCALAPPDATA + "\\Google\\Chrome\\Application\\chrome.exe"
  ];

  let executablePath = null;
  for (const path of chromePaths) {
    try {
      if (fs.existsSync(path)) {
        executablePath = path;
        console.log(`Chrome encontrado em: ${path}`);
        break;
      }
    } catch (err) {
      // Continua tentando outros caminhos
    }
  }

  const launchOptions = {
    headless: false, // precisa ser "false" para permitir clicar nos botões
    args: [
      "--kiosk-printing", // impressão silenciosa
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-dev-shm-usage",
      "--disable-accelerated-2d-canvas",
      "--no-first-run",
      "--no-zygote",
      "--disable-gpu"
    ],
    defaultViewport: null,
    timeout: 60000 // Aumentar timeout para 60 segundos
  };

  if (executablePath) {
    launchOptions.executablePath = executablePath;
  } else {
    console.log("Chrome não encontrado nos caminhos padrão. Usando Chromium do Puppeteer...");
  }

  try {
    const browser = await puppeteer.launch(launchOptions);
    console.log("Navegador aberto com sucesso!");

    const page = await browser.newPage();

    // Configurar timeout e user agent
    page.setDefaultNavigationTimeout(60000); // 60 segundos
    page.setDefaultTimeout(60000);
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

    console.log("Navegando para a página de login...");

    try {
      await page.goto("https://doity.com.br/admin/users/login", {
        waitUntil: 'networkidle2',
        timeout: 60000
      });
      console.log("Página de login carregada com sucesso!");
    } catch (error) {
      console.log("Erro ao carregar página de login, tentando novamente...");
      await page.goto("https://doity.com.br/admin/users/login", {
        waitUntil: 'domcontentloaded',
        timeout: 60000
      });
    }

    // Aguardar campos de login aparecerem
    await page.waitForSelector("#UserUsername", { timeout: 30000 });
    await page.waitForSelector("#UserPassword", { timeout: 30000 });

    console.log("Preenchendo credenciais...");
    await page.type("#UserUsername", "<EMAIL>");
    await page.type("#UserPassword", "Davicomunic22#");

    console.log("Fazendo login...");
    await page.click('input[value="Entrar"]');

    try {
      await page.waitForNavigation({ timeout: 30000 });
      console.log("Login realizado com sucesso!");
    } catch (error) {
      console.log("Aguardando carregamento da página após login...");
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    let currentPage = await askQuestion("Digite o número da página para começar: ");
    currentPage = parseInt(currentPage);
    if (isNaN(currentPage) || currentPage < 1) currentPage = 1;

    let hasNext = true;

    while (hasNext) {

      // Navegar para a página atual
      console.log(`Navegando para a página ${currentPage}...`);
      try {
        await page.goto(`https://doity.com.br/admin/credenciamento/index/263089/page:${currentPage}`, {
          waitUntil: "networkidle2",
          timeout: 60000
        });
        console.log(`Página ${currentPage} carregada com sucesso!`);
      } catch (error) {
        console.log(`Erro ao carregar página ${currentPage}, tentando com domcontentloaded...`);
        await page.goto(`https://doity.com.br/admin/credenciamento/index/263089/page:${currentPage}`, {
          waitUntil: "domcontentloaded",
          timeout: 60000
        });
      }

      // ---- REMOVER CHAT FLUTUANTE ----
      try {
        await page.evaluate(() => {
          const chat = document.querySelector(".live-chat-widget, #chat-widget");
          if (chat) chat.remove();
        });
      } catch (err) {
        console.log("Chat não encontrado ou já removido.");
      }

      // ---- LOOP DE IMPRESSÃO ----
      const printButtons = await page.$$("#bt-imprimir-etiqueta");
      for (let i = 0; i < printButtons.length; i++) {
        const currentButtons = await page.$$("#bt-imprimir-etiqueta");
        const button = currentButtons[i];
        if (!button) continue;

        console.log(`Página ${currentPage} - Imprimindo inscrito ${i + 1}`);

        // Configurar listener para detectar popup/nova janela ANTES de clicar
        const newPagePromise = new Promise(resolve => {
          const onTargetCreated = async (target) => {
            if (target.type() === 'page') {
              const newPage = await target.page();
              if (newPage && newPage !== page) {
                browser.off('targetcreated', onTargetCreated);
                resolve(newPage);
              }
            }
          };
          browser.on('targetcreated', onTargetCreated);

          // Timeout para caso não apareça popup
          setTimeout(() => {
            browser.off('targetcreated', onTargetCreated);
            resolve(null);
          }, 5000);
        });

        // Clicar no botão de impressão
        await page.evaluate((btn) => btn.click(), button);

        // Aguardar possível popup
        console.log(`Aguardando possível popup de login...`);
        const popupPage = await newPagePromise;

        if (popupPage) {
          console.log(`Popup detectado! Preenchendo login automaticamente...`);

          try {
            // Aguardar campos de login aparecerem no popup
            await popupPage.waitForSelector("#UserUsername", { timeout: 10000 });
            await popupPage.waitForSelector("#UserPassword", { timeout: 10000 });

            console.log("Preenchendo credenciais no popup...");
            await popupPage.type("#UserUsername", "<EMAIL>");
            await popupPage.type("#UserPassword", "Davicomunic22#");

            console.log("Fazendo login no popup...");
            await popupPage.click('input[value="Entrar"]');

            // Aguardar login ser processado
            try {
              await popupPage.waitForNavigation({ timeout: 10000 });
              console.log("Login no popup realizado com sucesso!");
            } catch (error) {
              console.log("Aguardando carregamento após login no popup...");
              await new Promise(resolve => setTimeout(resolve, 3000));
            }

            // Aguardar um pouco mais para garantir que a impressão seja processada
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Fechar o popup se ainda estiver aberto
            if (!popupPage.isClosed()) {
              await popupPage.close();
              console.log("Popup fechado.");
            }

          } catch (error) {
            console.log("Erro ao processar popup de login:", error.message);
            // Tentar fechar o popup mesmo em caso de erro
            try {
              if (!popupPage.isClosed()) {
                await popupPage.close();
              }
            } catch (closeError) {
              console.log("Erro ao fechar popup:", closeError.message);
            }
          }
        } else {
          console.log(`Nenhum popup detectado, continuando...`);
        }

        // Aguardar um pouco antes do próximo botão
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      // ---- PERGUNTAR AO USUÁRIO SE QUER AVANÇAR ----
      const answer = await askQuestion("Pressione ENTER para ir para a próxima página ou digite 'sair' para encerrar: ");
      if (answer.toLowerCase() === "sair") {
        hasNext = false;
      } else {
        currentPage++; // incrementar página manualmente
      }
    }

    await browser.close();
  } catch (error) {
    console.error("Erro ao executar o script:", error);
    process.exit(1);
  }
})();